/**
 * <PERSON> Intelligence Service
 * Core data service for rich narrative analysis and visualization
 */

interface CharacterEvolution {
  initial_state: string;
  major_turning_points: Array<{
    chapter: number;
    event: string;
    impact: string;
  }>;
  final_state: string;
  growth_trajectory: string;
}

interface RelationshipEvolution {
  initial_dynamic: string;
  key_developments: Array<{
    chapter: number;
    event: string;
    relationship_change: number;
    new_dynamic: string;
  }>;
  current_status: string;
}

interface Character {
  character_id: string;
  character_name: string;
  all_aliases: string[];
  overall_importance: number;
  character_archetype: string;
  first_appearance_chapter: number;
  last_appearance_chapter: number;
  total_chapters_present: number;
  chapter_by_chapter_summary: Array<{
    chapter_number: number;
    presence_level: string;
    key_actions: string[];
    emotional_state: string;
    character_goals: string[];
    development_notes: string;
  }>;
  character_evolution: CharacterEvolution;
  core_motivations: string[];
  primary_conflicts: string[];
  speaking_patterns: string;
  relationship_centrality: number;
}

interface Relationship {
  relationship_id: string;
  character_a_id: string;
  character_b_id: string;
  relationship_classification: string;
  relationship_summary: string;
  relationship_evolution: RelationshipEvolution;
  interaction_timeline: Array<{
    chapter: number;
    interaction_type: string;
    interaction_summary: string;
    emotional_intensity: number;
    plot_significance: number;
  }>;
  overall_strength: number;
  relationship_stability: string;
  mutual_influence: string;
  shared_history: string[];
  future_implications: string;
}

interface StoryData {
  book_metadata: {
    book_title: string;
    chapters_analyzed: number;
    chapter_range: string;
    overall_themes: string[];
    major_plot_arcs: string[];
    primary_settings: string[];
    narrative_progression: string;
  };
  consolidated_characters: Character[];
  consolidated_relationships: Relationship[];
  network_analysis: {
    most_connected_characters: Array<{
      character_id: string;
      connection_count: number;
      network_influence: number;
    }>;
    character_clusters: Array<{
      cluster_name: string;
      members: string[];
      cluster_type: string;
      binding_factor: string;
    }>;
    relationship_patterns: string[];
  };
  narrative_insights: {
    character_development_trends: string[];
    relationship_dynamics: string[];
    plot_driving_relationships: Array<{
      relationship_id: string;
      plot_impact: string;
    }>;
    character_agency_ranking: Array<{
      character_id: string;
      agency_score: number;
      influence_type: string;
    }>;
  };
}

class StoryIntelligenceService {
  private storyData: StoryData | null = null;
  private characterIndex: Map<string, Character> = new Map();
  private relationshipIndex: Map<string, Relationship> = new Map();
  private chapterIndex: Map<number, any> = new Map();
  private loaded = false;

  async initialize(): Promise<void> {
    if (this.loaded) return;

    try {
      console.log('🔄 Loading story intelligence data...');
      const response = await fetch('./hp_data/all_chapters.json');

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      this.storyData = await response.json();

      if (!this.storyData || !this.storyData.consolidated_characters || !this.storyData.consolidated_relationships) {
        throw new Error('Invalid story data structure');
      }

      this.buildIndexes();
      this.loaded = true;
      console.log('📚 Story Intelligence Service initialized with',
        this.storyData.consolidated_characters.length, 'characters and',
        this.storyData.consolidated_relationships.length, 'relationships');
    } catch (error) {
      console.error('❌ Failed to load story data:', error);
      throw error;
    }
  }

  private buildIndexes(): void {
    if (!this.storyData) return;

    // Build character index
    this.storyData.consolidated_characters.forEach(char => {
      this.characterIndex.set(char.character_id, char);
    });

    // Build relationship index
    this.storyData.consolidated_relationships.forEach(rel => {
      this.relationshipIndex.set(rel.relationship_id, rel);
    });

    // Build chapter index for quick lookups
    this.storyData.consolidated_characters.forEach(char => {
      char.chapter_by_chapter_summary.forEach(chapterSummary => {
        const chapterNum = chapterSummary.chapter_number;
        if (!this.chapterIndex.has(chapterNum)) {
          this.chapterIndex.set(chapterNum, {
            characters: [],
            relationships: []
          });
        }
        this.chapterIndex.get(chapterNum)!.characters.push({
          character_id: char.character_id,
          ...chapterSummary
        });
      });
    });
  }

  // Core data access methods
  getCharacter(characterId: string): Character | undefined {
    return this.characterIndex.get(characterId);
  }

  getRelationship(relationshipId: string): Relationship | undefined {
    return this.relationshipIndex.get(relationshipId);
  }

  getAllCharacters(): Character[] {
    return this.storyData?.consolidated_characters || [];
  }

  getAllRelationships(): Relationship[] {
    return this.storyData?.consolidated_relationships || [];
  }

  getBookMetadata() {
    return this.storyData?.book_metadata;
  }

  getNetworkAnalysis() {
    return this.storyData?.network_analysis;
  }

  getNarrativeInsights() {
    return this.storyData?.narrative_insights;
  }

  // Character evolution methods
  getCharacterEvolution(characterId: string) {
    const character = this.getCharacter(characterId);
    return character?.character_evolution;
  }

  getCharacterTurningPoints(characterId: string) {
    const evolution = this.getCharacterEvolution(characterId);
    return evolution?.major_turning_points || [];
  }

  getCharactersByChapter(chapterNumber: number) {
    return this.chapterIndex.get(chapterNumber)?.characters || [];
  }

  // Relationship dynamics methods
  getRelationshipEvolution(relationshipId: string) {
    const relationship = this.getRelationship(relationshipId);
    return relationship?.relationship_evolution;
  }

  getRelationshipsByCharacter(characterId: string): Relationship[] {
    return this.getAllRelationships().filter(rel => 
      rel.character_a_id === characterId || rel.character_b_id === characterId
    );
  }

  getStrongestRelationships(limit: number = 10): Relationship[] {
    return this.getAllRelationships()
      .sort((a, b) => b.overall_strength - a.overall_strength)
      .slice(0, limit);
  }

  // Network analysis methods
  getMostInfluentialCharacters(limit: number = 10): Character[] {
    if (!this.storyData?.character_network_analysis?.most_connected_characters) return [];

    return this.storyData.character_network_analysis.most_connected_characters
      .slice(0, limit)
      .map(item => this.characterIndex.get(item.character_id))
      .filter(char => char !== undefined) as Character[];
  }

  getCharacterClusters() {
    return this.storyData?.character_network_analysis?.character_clusters || [];
  }

  getRelationshipPatterns() {
    return this.storyData?.character_network_analysis?.relationship_patterns || [];
  }

  // Theme and narrative methods
  getOverallThemes() {
    return this.storyData?.book_metadata.overall_themes || [];
  }

  getMajorPlotArcs() {
    return this.storyData?.book_metadata.major_plot_arcs || [];
  }

  getCharacterAgencyRanking() {
    return this.storyData?.narrative_insights?.character_agency_ranking || [];
  }

  getThematicAnalysis() {
    if (!this.storyData) return null;
    return {
      themes: this.storyData.book_metadata?.overall_themes || [],
      plotArcs: this.storyData.book_metadata?.major_plot_arcs || [],
      characterDevelopment: this.storyData.narrative_insights?.character_development_trends || [],
      relationshipDynamics: this.storyData.narrative_insights?.relationship_dynamics || []
    };
  }

  // Search and filter methods
  searchCharacters(query: string): Character[] {
    const lowerQuery = query.toLowerCase();
    return this.getAllCharacters().filter(char => 
      char.character_name.toLowerCase().includes(lowerQuery) ||
      char.all_aliases.some(alias => alias.toLowerCase().includes(lowerQuery))
    );
  }

  getCharactersByArchetype(archetype: string): Character[] {
    return this.getAllCharacters().filter(char => 
      char.character_archetype.includes(archetype)
    );
  }

  getRelationshipsByType(type: string): Relationship[] {
    return this.getAllRelationships().filter(rel => 
      rel.relationship_classification === type
    );
  }
}

// Create singleton instance
export const storyIntelligence = new StoryIntelligenceService();
export type { Character, Relationship, StoryData, CharacterEvolution, RelationshipEvolution };
